export const ADMIN_WHATSAPP = "+***********"

export function createWhatsAppLink(phoneNumber: string, message: string): string {
  const encodedMessage = encodeURIComponent(message)
  return `https://wa.me/${phoneNumber.replace(/[^0-9]/g, "")}?text=${encodedMessage}`
}

export async function sendWhatsAppMessage(phoneNumber: string, message: string): Promise<void> {
  // In a real implementation, this would integrate with WhatsApp Business API
  // For now, we'll open WhatsApp Web with the pre-filled message
  const whatsappUrl = createWhatsAppLink(phoneNumber, message)

  // Open WhatsApp in a new window
  if (typeof window !== "undefined") {
    window.open(whatsappUrl, "_blank")
  }

  // In production, you would integrate with WhatsApp Business API here
  // Example: await fetch('/api/whatsapp/send', { method: 'POST', body: JSON.stringify({ phoneNumber, message }) })
}

export function sendBookingNotificationToSeller(
  sellerWhatsapp: string,
  booking: {
    customerName: string
    eventDate: string
    packageName: string
    totalAmount: number
    bookingId: string
  },
): string {
  const message = `🔔 New Booking Request!

Customer: ${booking.customerName}
Event Date: ${booking.eventDate}
Package: ${booking.packageName}
Amount: LKR ${booking.totalAmount.toLocaleString()}

Booking ID: ${booking.bookingId}

Please confirm availability by logging into your seller dashboard: ${window.location.origin}/seller/bookings

To approve this booking, click here: ${window.location.origin}/seller/confirm-booking/${booking.bookingId}`

  return createWhatsAppLink(sellerWhatsapp, message)
}

export function sendPaymentLinkToCustomer(
  customerWhatsapp: string,
  booking: {
    customerName: string
    packageName: string
    totalAmount: number
    paymentLink: string
  },
): string {
  const message = `✅ Your booking has been confirmed!

Hi ${booking.customerName},

Your booking for ${booking.packageName} has been approved by the service provider.
Total Amount: LKR ${booking.totalAmount.toLocaleString()}

Please complete payment using this link:
${booking.paymentLink}

For any questions, contact our support: ${createWhatsAppLink(ADMIN_WHATSAPP, "I need help with my booking")}`

  return createWhatsAppLink(customerWhatsapp, message)
}

export function createSupportLink(message = "I need help"): string {
  return createWhatsAppLink(ADMIN_WHATSAPP, message)
}
