"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { collection, addDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { useAuth } from "@/contexts/AuthContext"
import { SellerLayout } from "@/components/seller/SellerLayout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "@/hooks/use-toast"
import type { ServiceCategory, ServicePackage } from "@/types"
import { CategorySpecificFields } from "@/components/seller/CategorySpecificFields"
import { PackageManager } from "@/components/seller/PackageManager"

const serviceCategories: Array<{ value: ServiceCategory; label: string }> = [
  { value: "event-photographers", label: "Event Photography" },
  { value: "wedding-photographers", label: "Wedding Photography" },
  { value: "wedding-videographers", label: "Wedding Videography" },
  { value: "event-videographers", label: "Event Videography" },
  { value: "wedding-dronography", label: "Wedding Dronography" },
  { value: "event-dronography", label: "Event Dronography" },
  { value: "vehicle-renting", label: "Vehicle Renting" },
  { value: "flower-decorators", label: "Flower Decorators" },
  { value: "makeup-artists", label: "Makeup Artists" },
  { value: "wedding-dress-tailoring", label: "Wedding Dress Services" },
  { value: "product-shoot-models", label: "Product Shoot Models" },
  { value: "product-photography", label: "Product Photography" },
  { value: "photo-framing", label: "Photo Framing" },
  { value: "photo-video-editing", label: "Photo & Video Editing" },
  { value: "album-making", label: "Album Making" },
]

export default function CreateAdvertisementPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    category: "" as ServiceCategory,
    location: "",
    coverMedia: "",
    gallery: [] as string[],
    packages: [] as ServicePackage[],
    categorySpecific: {} as Record<string, any>,
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleCategoryChange = (category: ServiceCategory) => {
    setFormData((prev) => ({ ...prev, category, categorySpecific: {} }))
  }

  const handleCategorySpecificChange = (data: Record<string, any>) => {
    setFormData((prev) => ({ ...prev, categorySpecific: data }))
  }

  const handlePackagesChange = (packages: ServicePackage[]) => {
    setFormData((prev) => ({ ...prev, packages }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    if (!formData.category) {
      toast({
        title: "Category Required",
        description: "Please select a service category",
        variant: "destructive",
      })
      return
    }

    if (formData.packages.length === 0) {
      toast({
        title: "Packages Required",
        description: "Please add at least one service package",
        variant: "destructive",
      })
      return
    }

    setLoading(true)

    try {
      const advertisementData = {
        sellerId: user.id,
        title: formData.title,
        description: formData.description,
        category: formData.category,
        location: formData.location,
        coverMedia: formData.coverMedia || "/professional-photography-service.png",
        gallery: formData.gallery,
        packages: formData.packages,
        categorySpecific: formData.categorySpecific,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      await addDoc(collection(db, "advertisements"), advertisementData)

      toast({
        title: "Success",
        description: "Advertisement created successfully",
      })

      router.push("/seller/advertisements")
    } catch (error) {
      console.error("Error creating advertisement:", error)
      toast({
        title: "Error",
        description: "Failed to create advertisement",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <SellerLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Create Advertisement</h1>
          <p className="text-gray-400">Create a new service advertisement</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title" className="text-gray-300">
                  Service Title *
                </Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  className="bg-gray-800 border-gray-700 text-white"
                  placeholder="e.g., Professional Wedding Photography"
                />
              </div>

              <div>
                <Label htmlFor="description" className="text-gray-300">
                  Description *
                </Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  required
                  rows={4}
                  className="bg-gray-800 border-gray-700 text-white"
                  placeholder="Describe your service in detail..."
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-gray-300">Service Category *</Label>
                  <Select value={formData.category} onValueChange={handleCategoryChange}>
                    <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-900 border-gray-700">
                      {serviceCategories.map((category) => (
                        <SelectItem key={category.value} value={category.value} className="text-white">
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="location" className="text-gray-300">
                    Location *
                  </Label>
                  <Input
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    required
                    className="bg-gray-800 border-gray-700 text-white"
                    placeholder="e.g., Colombo, Sri Lanka"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="coverMedia" className="text-gray-300">
                  Cover Image URL
                </Label>
                <Input
                  id="coverMedia"
                  name="coverMedia"
                  value={formData.coverMedia}
                  onChange={handleInputChange}
                  className="bg-gray-800 border-gray-700 text-white"
                  placeholder="https://example.com/image.jpg"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Upload your cover image to a hosting service and paste the URL here
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Category-Specific Fields */}
          {formData.category && (
            <CategorySpecificFields
              category={formData.category}
              data={formData.categorySpecific}
              onChange={handleCategorySpecificChange}
            />
          )}

          {/* Service Packages */}
          <PackageManager packages={formData.packages} onChange={handlePackagesChange} />

          <div className="flex gap-4">
            <Button type="submit" disabled={loading}>
              {loading ? "Creating..." : "Create Advertisement"}
            </Button>
            <Button type="button" variant="outline" onClick={() => router.back()}>
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </SellerLayout>
  )
}
