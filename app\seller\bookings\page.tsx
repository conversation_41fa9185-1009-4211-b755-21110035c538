"use client"

import { useEffect, useState } from "react"
import { collection, getDocs, query, where, doc, updateDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { useAuth } from "@/contexts/AuthContext"
import { SellerLayout } from "@/components/seller/SellerLayout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { CheckCircle, XCircle, Eye, MessageCircle } from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { toast } from "@/hooks/use-toast"
import { sendPaymentLinkToCustomer } from "@/lib/whatsapp"
import type { Booking } from "@/types"

export default function SellerBookingsPage() {
  const { user } = useAuth()
  const [bookings, setBookings] = useState<Booking[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null)

  useEffect(() => {
    const fetchBookings = async () => {
      if (!user) return

      try {
        const q = query(collection(db, "bookings"), where("sellerId", "==", user.id))
        const querySnapshot = await getDocs(q)
        const bookingsData = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
          eventDate: doc.data().eventDate?.toDate(),
          createdAt: doc.data().createdAt?.toDate(),
          updatedAt: doc.data().updatedAt?.toDate(),
        })) as Booking[]

        // Sort by creation date (newest first)
        bookingsData.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        setBookings(bookingsData)
      } catch (error) {
        console.error("Error fetching bookings:", error)
        toast({
          title: "Error",
          description: "Failed to fetch bookings",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchBookings()
  }, [user])

  const handleApproveBooking = async (bookingId: string) => {
    try {
      await updateDoc(doc(db, "bookings", bookingId), {
        status: "pay-now",
        updatedAt: new Date(),
      })

      // Update local state
      setBookings((prev) =>
        prev.map((booking) => (booking.id === bookingId ? { ...booking, status: "pay-now" } : booking)),
      )

      // Find the booking to send payment notification
      const booking = bookings.find((b) => b.id === bookingId)
      if (booking) {
        // Generate payment link (in a real app, this would be a proper payment gateway link)
        const paymentLink = `${window.location.origin}/payment/${bookingId}`

        // Send WhatsApp notification to customer
        const whatsappLink = sendPaymentLinkToCustomer(booking.customerWhatsapp, {
          customerName: booking.customerName,
          packageName: "Service Package", // You'd get this from the advertisement
          totalAmount: booking.totalAmount,
          paymentLink,
        })

        // Open WhatsApp link
        window.open(whatsappLink, "_blank")
      }

      toast({
        title: "Booking Approved",
        description: "Customer has been notified and can now proceed with payment",
      })
    } catch (error) {
      console.error("Error approving booking:", error)
      toast({
        title: "Error",
        description: "Failed to approve booking",
        variant: "destructive",
      })
    }
  }

  const handleRejectBooking = async (bookingId: string) => {
    if (!confirm("Are you sure you want to reject this booking?")) return

    try {
      await updateDoc(doc(db, "bookings", bookingId), {
        status: "cancelled",
        updatedAt: new Date(),
      })

      // Update local state
      setBookings((prev) =>
        prev.map((booking) => (booking.id === bookingId ? { ...booking, status: "cancelled" } : booking)),
      )

      toast({
        title: "Booking Rejected",
        description: "The booking has been cancelled",
      })
    } catch (error) {
      console.error("Error rejecting booking:", error)
      toast({
        title: "Error",
        description: "Failed to reject booking",
        variant: "destructive",
      })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "waiting":
        return "bg-yellow-600"
      case "approved":
        return "bg-green-600"
      case "pay-now":
        return "bg-blue-600"
      case "paid":
        return "bg-green-600"
      case "completed":
        return "bg-green-600"
      case "cancelled":
        return "bg-red-600"
      default:
        return "bg-gray-600"
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "waiting":
        return "Waiting"
      case "approved":
        return "Approved"
      case "pay-now":
        return "Payment Required"
      case "paid":
        return "Paid"
      case "completed":
        return "Completed"
      case "cancelled":
        return "Cancelled"
      default:
        return status
    }
  }

  return (
    <SellerLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Bookings</h1>
          <p className="text-gray-400">Manage your booking requests</p>
        </div>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-white">Booking Requests ({bookings.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="text-gray-400">Loading bookings...</div>
              </div>
            ) : bookings.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">No bookings yet</div>
                <p className="text-gray-500 text-sm">
                  Booking requests will appear here when customers book your services
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-gray-800">
                      <TableHead className="text-gray-300">Customer</TableHead>
                      <TableHead className="text-gray-300">Event Date</TableHead>
                      <TableHead className="text-gray-300">Amount</TableHead>
                      <TableHead className="text-gray-300">Status</TableHead>
                      <TableHead className="text-gray-300">Created</TableHead>
                      <TableHead className="text-gray-300">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {bookings.map((booking) => (
                      <TableRow key={booking.id} className="border-gray-800">
                        <TableCell>
                          <div>
                            <p className="text-white font-medium">{booking.customerName}</p>
                            <p className="text-gray-400 text-sm">{booking.customerPhone}</p>
                          </div>
                        </TableCell>
                        <TableCell className="text-gray-300">{booking.eventDate.toLocaleDateString()}</TableCell>
                        <TableCell className="text-white font-medium">
                          LKR {booking.totalAmount.toLocaleString()}
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(booking.status)}>{getStatusLabel(booking.status)}</Badge>
                        </TableCell>
                        <TableCell className="text-gray-300">{booking.createdAt.toLocaleDateString()}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button variant="ghost" size="sm" onClick={() => setSelectedBooking(booking)}>
                                  <Eye className="w-4 h-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="bg-gray-900 border-gray-800 max-w-2xl">
                                <DialogHeader>
                                  <DialogTitle className="text-white">Booking Details</DialogTitle>
                                </DialogHeader>
                                {selectedBooking && (
                                  <div className="space-y-4">
                                    <div className="grid grid-cols-2 gap-4">
                                      <div>
                                        <label className="text-sm text-gray-400">Customer Name</label>
                                        <p className="text-white">{selectedBooking.customerName}</p>
                                      </div>
                                      <div>
                                        <label className="text-sm text-gray-400">Event Date</label>
                                        <p className="text-white">{selectedBooking.eventDate.toLocaleDateString()}</p>
                                      </div>
                                      <div>
                                        <label className="text-sm text-gray-400">Phone</label>
                                        <p className="text-white">{selectedBooking.customerPhone}</p>
                                      </div>
                                      <div>
                                        <label className="text-sm text-gray-400">WhatsApp</label>
                                        <p className="text-white">{selectedBooking.customerWhatsapp}</p>
                                      </div>
                                    </div>
                                    <div>
                                      <label className="text-sm text-gray-400">Total Amount</label>
                                      <p className="text-xl font-semibold text-white">
                                        LKR {selectedBooking.totalAmount.toLocaleString()}
                                      </p>
                                    </div>
                                    {selectedBooking.notes && (
                                      <div>
                                        <label className="text-sm text-gray-400">Notes</label>
                                        <p className="text-white">{selectedBooking.notes}</p>
                                      </div>
                                    )}
                                    <div>
                                      <label className="text-sm text-gray-400">Status</label>
                                      <Badge className={getStatusColor(selectedBooking.status)}>
                                        {getStatusLabel(selectedBooking.status)}
                                      </Badge>
                                    </div>
                                  </div>
                                )}
                              </DialogContent>
                            </Dialog>

                            {booking.status === "waiting" && (
                              <>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleApproveBooking(booking.id)}
                                  className="text-green-500"
                                >
                                  <CheckCircle className="w-4 h-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRejectBooking(booking.id)}
                                  className="text-red-500"
                                >
                                  <XCircle className="w-4 h-4" />
                                </Button>
                              </>
                            )}

                            <a
                              href={`https://wa.me/${booking.customerWhatsapp.replace(/[^0-9]/g, "")}?text=Hello ${booking.customerName}, regarding your booking for ${booking.eventDate.toLocaleDateString()}`}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <Button variant="ghost" size="sm" className="text-green-500">
                                <MessageCircle className="w-4 h-4" />
                              </Button>
                            </a>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </SellerLayout>
  )
}
