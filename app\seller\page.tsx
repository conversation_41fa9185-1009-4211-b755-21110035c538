"use client"

import { useEffect, useState } from "react"
import { collection, getDocs, query, where } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { useAuth } from "@/contexts/AuthContext"
import { SellerLayout } from "@/components/seller/SellerLayout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Camera, ShoppingCart, Eye, TrendingUp, Calendar, AlertCircle } from "lucide-react"

interface SellerStats {
  totalAds: number
  activeAds: number
  totalBookings: number
  pendingBookings: number
  totalViews: number
  monthlyRevenue: number
}

export default function SellerDashboard() {
  const { user } = useAuth()
  const [stats, setStats] = useState<SellerStats>({
    totalAds: 0,
    activeAds: 0,
    totalBookings: 0,
    pendingBookings: 0,
    totalViews: 0,
    monthlyRevenue: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      if (!user) return

      try {
        // Fetch seller's advertisements
        const adsQuery = query(collection(db, "advertisements"), where("sellerId", "==", user.id))
        const adsSnapshot = await getDocs(adsQuery)
        const ads = adsSnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))

        // Fetch seller's bookings
        const bookingsQuery = query(collection(db, "bookings"), where("sellerId", "==", user.id))
        const bookingsSnapshot = await getDocs(bookingsQuery)
        const bookings = bookingsSnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))

        // Calculate stats
        const activeAds = ads.filter((ad: any) => ad.isActive).length
        const pendingBookings = bookings.filter((booking: any) => booking.status === "waiting").length

        // Calculate monthly revenue (last 30 days)
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        const monthlyRevenue = bookings
          .filter((booking: any) => {
            const bookingDate = booking.createdAt?.toDate?.() || new Date(booking.createdAt)
            return booking.status === "paid" && bookingDate >= thirtyDaysAgo
          })
          .reduce((sum: number, booking: any) => sum + (booking.totalAmount || 0), 0)

        setStats({
          totalAds: ads.length,
          activeAds,
          totalBookings: bookings.length,
          pendingBookings,
          totalViews: ads.reduce((sum: number, ad: any) => sum + (ad.views || 0), 0),
          monthlyRevenue,
        })
      } catch (error) {
        console.error("Error fetching seller stats:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [user])

  const statCards = [
    {
      title: "Total Advertisements",
      value: stats.totalAds,
      subtitle: `${stats.activeAds} active`,
      icon: Camera,
      color: "text-blue-500",
    },
    {
      title: "Total Bookings",
      value: stats.totalBookings,
      subtitle: `${stats.pendingBookings} pending`,
      icon: ShoppingCart,
      color: "text-green-500",
    },
    {
      title: "Total Views",
      value: stats.totalViews,
      subtitle: "All time",
      icon: Eye,
      color: "text-yellow-500",
    },
    {
      title: "Monthly Revenue",
      value: `LKR ${stats.monthlyRevenue.toLocaleString()}`,
      subtitle: "Last 30 days",
      icon: TrendingUp,
      color: "text-purple-500",
    },
  ]

  return (
    <SellerLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
          <p className="text-gray-400">Welcome to your seller dashboard</p>
        </div>

        {/* Account Status Alert */}
        {user && !user.isApproved && (
          <Card className="bg-yellow-900/20 border-yellow-800">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <AlertCircle className="w-5 h-5 text-yellow-500" />
                <div>
                  <p className="text-yellow-200 font-medium">Account Pending Approval</p>
                  <p className="text-yellow-300 text-sm">
                    Your account is under review. You'll be able to create advertisements once approved by admin.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="bg-gray-900 border-gray-800 animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-800 rounded mb-2" />
                  <div className="h-8 bg-gray-800 rounded mb-2" />
                  <div className="h-3 bg-gray-800 rounded w-2/3" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {statCards.map((card, index) => (
              <Card key={index} className="bg-gray-900 border-gray-800">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-400">{card.title}</p>
                      <p className="text-2xl font-bold text-white">{card.value}</p>
                      <p className="text-xs text-gray-500">{card.subtitle}</p>
                    </div>
                    <card.icon className={`w-8 h-8 ${card.color}`} />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <div className="flex-1">
                    <p className="text-sm text-white">New booking received</p>
                    <p className="text-xs text-gray-400">2 hours ago</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                  <div className="flex-1">
                    <p className="text-sm text-white">Advertisement viewed</p>
                    <p className="text-xs text-gray-400">4 hours ago</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                  <div className="flex-1">
                    <p className="text-sm text-white">Profile updated</p>
                    <p className="text-xs text-gray-400">1 day ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <button className="w-full p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-left transition-colors">
                  <p className="text-sm font-medium text-white">Create New Advertisement</p>
                  <p className="text-xs text-gray-400">Add a new service to your portfolio</p>
                </button>
                <button className="w-full p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-left transition-colors">
                  <p className="text-sm font-medium text-white">Update Gallery</p>
                  <p className="text-xs text-gray-400">Add new photos to showcase your work</p>
                </button>
                <button className="w-full p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-left transition-colors">
                  <p className="text-sm font-medium text-white">Review Bookings</p>
                  <p className="text-xs text-gray-400">Check pending booking requests</p>
                </button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </SellerLayout>
  )
}
