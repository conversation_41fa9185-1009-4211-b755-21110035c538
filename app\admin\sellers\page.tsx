"use client"

import { useEffect, useState } from "react"
import { collection, getDocs, query, where, doc, updateDoc, deleteDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Search, Trash2, CheckCircle, XCircle, Eye } from "lucide-react"
import { toast } from "@/hooks/use-toast"

interface Seller {
  id: string
  name: string
  email: string
  businessName: string
  phone?: string
  whatsapp?: string
  categories: string[]
  isApproved: boolean
  createdAt: any
}

export default function AdminSellersPage() {
  const [sellers, setSellers] = useState<Seller[]>([])
  const [filteredSellers, setFilteredSellers] = useState<Seller[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedSeller, setSelectedSeller] = useState<Seller | null>(null)

  useEffect(() => {
    const fetchSellers = async () => {
      try {
        if (!db) {
          console.error("Database not initialized")
          setLoading(false)
          return
        }

        const q = query(collection(db, "users"), where("role", "==", "seller"))
        const querySnapshot = await getDocs(q)
        const sellersData = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Seller[]

        setSellers(sellersData)
        setFilteredSellers(sellersData)
      } catch (error) {
        console.error("Error fetching sellers:", error)
        toast({
          title: "Error",
          description: "Failed to fetch sellers",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchSellers()
  }, [])

  useEffect(() => {
    if (searchQuery) {
      const filtered = sellers.filter(
        (seller) =>
          seller.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          seller.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
          seller.businessName.toLowerCase().includes(searchQuery.toLowerCase()),
      )
      setFilteredSellers(filtered)
    } else {
      setFilteredSellers(sellers)
    }
  }, [searchQuery, sellers])

  const handleApprovalToggle = async (sellerId: string, currentStatus: boolean) => {
    try {
      if (!db) {
        throw new Error("Database not initialized")
      }

      await updateDoc(doc(db, "users", sellerId), {
        isApproved: !currentStatus,
        updatedAt: new Date(),
      })

      setSellers((prev) =>
        prev.map((seller) => (seller.id === sellerId ? { ...seller, isApproved: !currentStatus } : seller)),
      )

      toast({
        title: "Success",
        description: `Seller ${!currentStatus ? "approved" : "suspended"} successfully`,
      })
    } catch (error) {
      console.error("Error updating seller approval:", error)
      toast({
        title: "Error",
        description: "Failed to update seller status",
        variant: "destructive",
      })
    }
  }

  const handleDeleteSeller = async (sellerId: string) => {
    if (!confirm("Are you sure you want to delete this seller? This action cannot be undone.")) {
      return
    }

    try {
      if (!db) {
        throw new Error("Database not initialized")
      }

      await deleteDoc(doc(db, "users", sellerId))
      setSellers((prev) => prev.filter((seller) => seller.id !== sellerId))

      toast({
        title: "Success",
        description: "Seller deleted successfully",
      })
    } catch (error) {
      console.error("Error deleting seller:", error)
      toast({
        title: "Error",
        description: "Failed to delete seller",
        variant: "destructive",
      })
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Sellers Management</h1>
            <p className="text-gray-400">Manage seller accounts and approvals</p>
          </div>
        </div>

        {/* Search and Filters */}
        <Card className="bg-gray-900 border-gray-800">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  type="text"
                  placeholder="Search sellers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-gray-800 border-gray-700 text-white"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sellers Table */}
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-white">Sellers ({filteredSellers.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="text-gray-400">Loading sellers...</div>
              </div>
            ) : filteredSellers.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-gray-400">No sellers found</div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-gray-800">
                      <TableHead className="text-gray-300">Name</TableHead>
                      <TableHead className="text-gray-300">Business</TableHead>
                      <TableHead className="text-gray-300">Email</TableHead>
                      <TableHead className="text-gray-300">Status</TableHead>
                      <TableHead className="text-gray-300">Categories</TableHead>
                      <TableHead className="text-gray-300">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSellers.map((seller) => (
                      <TableRow key={seller.id} className="border-gray-800">
                        <TableCell className="text-white font-medium">{seller.name}</TableCell>
                        <TableCell className="text-gray-300">{seller.businessName}</TableCell>
                        <TableCell className="text-gray-300">{seller.email}</TableCell>
                        <TableCell>
                          <Badge
                            variant={seller.isApproved ? "default" : "secondary"}
                            className={seller.isApproved ? "bg-green-600" : "bg-yellow-600"}
                          >
                            {seller.isApproved ? "Approved" : "Pending"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-gray-300">
                          <div className="flex flex-wrap gap-1">
                            {seller.categories?.slice(0, 2).map((category) => (
                              <Badge key={category} variant="outline" className="text-xs">
                                {category}
                              </Badge>
                            ))}
                            {seller.categories?.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{seller.categories.length - 2}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button variant="ghost" size="sm" onClick={() => setSelectedSeller(seller)}>
                                  <Eye className="w-4 h-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="bg-gray-900 border-gray-800">
                                <DialogHeader>
                                  <DialogTitle className="text-white">Seller Details</DialogTitle>
                                </DialogHeader>
                                {selectedSeller && (
                                  <div className="space-y-4">
                                    <div>
                                      <label className="text-sm text-gray-400">Name</label>
                                      <p className="text-white">{selectedSeller.name}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm text-gray-400">Business Name</label>
                                      <p className="text-white">{selectedSeller.businessName}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm text-gray-400">Email</label>
                                      <p className="text-white">{selectedSeller.email}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm text-gray-400">Phone</label>
                                      <p className="text-white">{selectedSeller.phone || "Not provided"}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm text-gray-400">WhatsApp</label>
                                      <p className="text-white">{selectedSeller.whatsapp || "Not provided"}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm text-gray-400">Categories</label>
                                      <div className="flex flex-wrap gap-2 mt-1">
                                        {selectedSeller.categories?.map((category) => (
                                          <Badge key={category} variant="outline">
                                            {category}
                                          </Badge>
                                        ))}
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </DialogContent>
                            </Dialog>

                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleApprovalToggle(seller.id, seller.isApproved)}
                              className={seller.isApproved ? "text-yellow-500" : "text-green-500"}
                            >
                              {seller.isApproved ? (
                                <XCircle className="w-4 h-4" />
                              ) : (
                                <CheckCircle className="w-4 h-4" />
                              )}
                            </Button>

                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteSeller(seller.id)}
                              className="text-red-500"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
