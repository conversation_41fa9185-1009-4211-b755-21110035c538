"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { doc, getDoc, updateDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle, XCircle, Calendar, User, Phone } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { sendPaymentLinkToCustomer } from "@/lib/whatsapp"
import type { Booking } from "@/types"

export default function ConfirmBookingPage() {
  const params = useParams()
  const router = useRouter()
  const bookingId = params.id as string
  const [booking, setBooking] = useState<Booking | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)

  useEffect(() => {
    const fetchBooking = async () => {
      try {
        const bookingDoc = await getDoc(doc(db, "bookings", bookingId))
        if (bookingDoc.exists()) {
          const bookingData = {
            id: bookingDoc.id,
            ...bookingDoc.data(),
            eventDate: bookingDoc.data().eventDate?.toDate(),
            createdAt: bookingDoc.data().createdAt?.toDate(),
            updatedAt: bookingDoc.data().updatedAt?.toDate(),
          } as Booking

          setBooking(bookingData)
        }
      } catch (error) {
        console.error("Error fetching booking:", error)
      } finally {
        setLoading(false)
      }
    }

    if (bookingId) {
      fetchBooking()
    }
  }, [bookingId])

  const handleConfirm = async () => {
    if (!booking) return

    setProcessing(true)

    try {
      await updateDoc(doc(db, "bookings", booking.id), {
        status: "pay-now",
        updatedAt: new Date(),
      })

      // Generate payment link (in a real app, this would be a proper payment gateway link)
      const paymentLink = `${window.location.origin}/payment/${booking.id}`

      // Send WhatsApp notification to customer
      const whatsappLink = sendPaymentLinkToCustomer(booking.customerWhatsapp, {
        customerName: booking.customerName,
        packageName: "Service Package", // You'd get this from the advertisement
        totalAmount: booking.totalAmount,
        paymentLink,
      })

      // Open WhatsApp link
      window.open(whatsappLink, "_blank")

      toast({
        title: "Booking Confirmed",
        description: "Customer has been notified and can now proceed with payment",
      })

      // Redirect to seller dashboard
      setTimeout(() => {
        router.push("/seller/bookings")
      }, 2000)
    } catch (error) {
      console.error("Error confirming booking:", error)
      toast({
        title: "Error",
        description: "Failed to confirm booking",
        variant: "destructive",
      })
    } finally {
      setProcessing(false)
    }
  }

  const handleReject = async () => {
    if (!booking) return

    setProcessing(true)

    try {
      await updateDoc(doc(db, "bookings", booking.id), {
        status: "cancelled",
        updatedAt: new Date(),
      })

      toast({
        title: "Booking Rejected",
        description: "The booking has been cancelled",
      })

      // Redirect to seller dashboard
      setTimeout(() => {
        router.push("/seller/bookings")
      }, 2000)
    } catch (error) {
      console.error("Error rejecting booking:", error)
      toast({
        title: "Error",
        description: "Failed to reject booking",
        variant: "destructive",
      })
    } finally {
      setProcessing(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white">Loading booking details...</div>
      </div>
    )
  }

  if (!booking) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <Card className="bg-gray-900 border-gray-800 max-w-md">
          <CardContent className="p-8 text-center">
            <h2 className="text-xl font-semibold text-white mb-2">Booking Not Found</h2>
            <p className="text-gray-400">The booking could not be found or has already been processed.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (booking.status !== "waiting") {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <Card className="bg-gray-900 border-gray-800 max-w-md">
          <CardContent className="p-8 text-center">
            <h2 className="text-xl font-semibold text-white mb-2">Booking Already Processed</h2>
            <p className="text-gray-400">This booking has already been {booking.status}.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <Card className="bg-gray-900 border-gray-800 max-w-2xl w-full">
        <CardHeader className="text-center">
          <CardTitle className="text-white text-2xl">Booking Confirmation</CardTitle>
          <p className="text-gray-400">Please review and confirm this booking request</p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-3">
              <User className="w-5 h-5 text-blue-500" />
              <div>
                <p className="text-gray-400 text-sm">Customer</p>
                <p className="text-white font-medium">{booking.customerName}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Calendar className="w-5 h-5 text-green-500" />
              <div>
                <p className="text-gray-400 text-sm">Event Date</p>
                <p className="text-white font-medium">
                  {booking.eventDate.toLocaleDateString("en-US", {
                    weekday: "long",
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Phone className="w-5 h-5 text-purple-500" />
              <div>
                <p className="text-gray-400 text-sm">Phone</p>
                <p className="text-white font-medium">{booking.customerPhone}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Phone className="w-5 h-5 text-green-500" />
              <div>
                <p className="text-gray-400 text-sm">WhatsApp</p>
                <p className="text-white font-medium">{booking.customerWhatsapp}</p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-4">
            <div className="flex justify-between items-center mb-4">
              <span className="text-gray-400">Total Amount</span>
              <span className="text-2xl font-bold text-white">LKR {booking.totalAmount.toLocaleString()}</span>
            </div>
          </div>

          {booking.notes && (
            <div className="border-t border-gray-800 pt-4">
              <p className="text-gray-400 text-sm mb-2">Customer Notes</p>
              <p className="text-white bg-gray-800 p-3 rounded-lg">{booking.notes}</p>
            </div>
          )}

          <div className="flex gap-4 pt-4">
            <Button
              onClick={handleConfirm}
              disabled={processing}
              className="flex-1 bg-green-600 hover:bg-green-700 flex items-center gap-2"
            >
              <CheckCircle className="w-4 h-4" />
              {processing ? "Confirming..." : "Confirm Booking"}
            </Button>
            <Button
              onClick={handleReject}
              disabled={processing}
              variant="outline"
              className="flex-1 border-red-600 text-red-500 hover:bg-red-600 hover:text-white flex items-center gap-2 bg-transparent"
            >
              <XCircle className="w-4 h-4" />
              {processing ? "Rejecting..." : "Reject Booking"}
            </Button>
          </div>

          <div className="text-center text-gray-400 text-sm">
            <p>By confirming, the customer will be notified via WhatsApp and can proceed with payment.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
