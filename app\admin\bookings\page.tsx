"use client"

import { useState, useEffect } from "react"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { collection, query, orderBy, onSnapshot, doc, updateDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { useAuth } from "@/contexts/AuthContext"
import type { Booking } from "@/types"
import { sendWhatsAppMessage } from "@/lib/whatsapp"
import { Eye, DollarSign, CheckCircle, Clock } from "lucide-react"

export default function AdminBookingsPage() {
  const { user, isAdmin } = useAuth()
  const [bookings, setBookings] = useState<Booking[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState("all")
  const [search, setSearch] = useState("")

  useEffect(() => {
    if (!isAdmin) return

    const q = query(collection(db, "bookings"), orderBy("createdAt", "desc"))
    const unsubscribe = onSnapshot(q, (snapshot) => {
      const bookingsData = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        eventDate: doc.data().eventDate?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate(),
      })) as Booking[]
      setBookings(bookingsData)
      setLoading(false)
    })

    return unsubscribe
  }, [isAdmin])

  const updateBookingStatus = async (bookingId: string, status: string, paymentAmount?: number) => {
    try {
      const updateData: any = {
        status,
        updatedAt: new Date(),
      }

      if (paymentAmount) {
        updateData.paymentAmount = paymentAmount
      }

      if (status === "paid") {
        updateData.paidAt = new Date()
      }

      await updateDoc(doc(db, "bookings", bookingId), updateData)

      // Send WhatsApp notification based on status
      const booking = bookings.find((b) => b.id === bookingId)
      if (booking) {
        let message = ""
        switch (status) {
          case "paid":
            message = `✅ Payment confirmed for booking #${bookingId.slice(-6)}. Your photography session is fully booked! We'll contact you soon with final details.`
            break
          case "cancelled":
            message = `❌ Booking #${bookingId.slice(-6)} has been cancelled. If you have any questions, please contact us.`
            break
          case "completed":
            message = `🎉 Your photography session is complete! Thank you for choosing our services. We hope you love your photos!`
            break
        }

        if (message) {
          await sendWhatsAppMessage(booking.whatsapp, message)
        }
      }
    } catch (error) {
      console.error("Error updating booking:", error)
    }
  }

  const filteredBookings = bookings.filter((booking) => {
    const matchesFilter = filter === "all" || booking.status === filter
    const matchesSearch =
      search === "" ||
      booking.customerName.toLowerCase().includes(search.toLowerCase()) ||
      booking.customerEmail.toLowerCase().includes(search.toLowerCase()) ||
      booking.id.toLowerCase().includes(search.toLowerCase())

    return matchesFilter && matchesSearch
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-500"
      case "confirmed":
        return "bg-blue-500"
      case "paid":
        return "bg-green-500"
      case "completed":
        return "bg-purple-500"
      case "cancelled":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const totalRevenue = bookings
    .filter((b) => b.status === "paid" || b.status === "completed")
    .reduce((sum, b) => sum + (b.paymentAmount || b.totalAmount), 0)

  const pendingPayments = bookings.filter((b) => b.status === "confirmed").reduce((sum, b) => sum + b.totalAmount, 0)

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-white">Booking & Payment Management</h1>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">Total Bookings</CardTitle>
              <Eye className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{bookings.length}</div>
            </CardContent>
          </Card>

          <Card className="bg-gray-800 border-gray-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-400">${totalRevenue.toLocaleString()}</div>
            </CardContent>
          </Card>

          <Card className="bg-gray-800 border-gray-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">Pending Payments</CardTitle>
              <Clock className="h-4 w-4 text-yellow-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-400">${pendingPayments.toLocaleString()}</div>
            </CardContent>
          </Card>

          <Card className="bg-gray-800 border-gray-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-400">
                {bookings.filter((b) => b.status === "completed").length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className="flex gap-4">
          <Input
            placeholder="Search bookings..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="max-w-sm bg-gray-800 border-gray-700 text-white"
          />
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-48 bg-gray-800 border-gray-700 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-700">
              <SelectItem value="all">All Bookings</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="confirmed">Confirmed</SelectItem>
              <SelectItem value="paid">Paid</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Bookings Table */}
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="border-gray-700">
                  <TableHead className="text-gray-300">Booking ID</TableHead>
                  <TableHead className="text-gray-300">Customer</TableHead>
                  <TableHead className="text-gray-300">Service</TableHead>
                  <TableHead className="text-gray-300">Event Date</TableHead>
                  <TableHead className="text-gray-300">Amount</TableHead>
                  <TableHead className="text-gray-300">Status</TableHead>
                  <TableHead className="text-gray-300">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredBookings.map((booking) => (
                  <TableRow key={booking.id} className="border-gray-700">
                    <TableCell className="text-white font-mono">#{booking.id.slice(-6)}</TableCell>
                    <TableCell className="text-white">
                      <div>
                        <div className="font-medium">{booking.customerName}</div>
                        <div className="text-sm text-gray-400">{booking.customerEmail}</div>
                      </div>
                    </TableCell>
                    <TableCell className="text-white">
                      <div className="text-sm">{booking.items.map((item) => item.serviceName).join(", ")}</div>
                    </TableCell>
                    <TableCell className="text-white">{booking.eventDate?.toLocaleDateString()}</TableCell>
                    <TableCell className="text-white">
                      <div>
                        <div className="font-medium">${booking.totalAmount.toLocaleString()}</div>
                        {booking.paymentAmount && booking.paymentAmount !== booking.totalAmount && (
                          <div className="text-sm text-green-400">Paid: ${booking.paymentAmount.toLocaleString()}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={`${getStatusColor(booking.status)} text-white`}>{booking.status}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {booking.status === "confirmed" && (
                          <Button
                            size="sm"
                            onClick={() => updateBookingStatus(booking.id, "paid", booking.totalAmount)}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            Mark Paid
                          </Button>
                        )}
                        {booking.status === "paid" && (
                          <Button
                            size="sm"
                            onClick={() => updateBookingStatus(booking.id, "completed")}
                            className="bg-purple-600 hover:bg-purple-700"
                          >
                            Complete
                          </Button>
                        )}
                        {booking.status !== "cancelled" && booking.status !== "completed" && (
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => updateBookingStatus(booking.id, "cancelled")}
                          >
                            Cancel
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
