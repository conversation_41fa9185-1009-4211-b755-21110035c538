export type UserRole = "admin" | "seller" | "user"

export type ServiceCategory =
  | "event-photographers"
  | "wedding-photographers"
  | "wedding-videographers"
  | "event-videographers"
  | "wedding-dronography"
  | "event-dronography"
  | "vehicle-renting"
  | "flower-decorators"
  | "makeup-artists"
  | "wedding-dress-tailoring"
  | "product-shoot-models"
  | "product-photography"
  | "photo-framing"
  | "photo-video-editing"
  | "album-making"

export interface User {
  id: string
  email: string
  role: UserRole
  name: string
  phone?: string
  whatsapp?: string
  createdAt: Date
  updatedAt: Date
}

export interface Seller extends User {
  businessName: string
  description: string
  categories: ServiceCategory[]
  isApproved: boolean
  profileImage?: string
  coverImage?: string
}

export interface ServicePackage {
  id: string
  name: string
  description: string
  price: number
  duration?: string
  features: string[]
}

export interface Advertisement {
  id: string
  sellerId: string
  category: ServiceCategory
  title: string
  description: string
  coverMedia: string // URL to cover photo/video
  gallery: string[] // Array of image URLs
  packages: ServicePackage[]
  location: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export type BookingStatus = "waiting" | "approved" | "pay-now" | "paid" | "completed" | "cancelled"

export interface Booking {
  id: string
  userId?: string // Optional since users don't need accounts
  sellerId: string
  advertisementId: string
  packageId: string
  customerName: string
  customerPhone: string
  customerWhatsapp: string
  eventDate: Date
  status: BookingStatus
  totalAmount: number
  paymentLink?: string
  notes?: string
  createdAt: Date
  updatedAt: Date
}

export interface CartItem {
  advertisementId: string
  packageId: string
  sellerId: string
  packageName: string
  price: number
  sellerName: string
  category: ServiceCategory
}
