"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { doc, updateDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { useAuth } from "@/contexts/AuthContext"
import { SellerLayout } from "@/components/seller/SellerLayout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from "@/hooks/use-toast"
import type { ServiceCategory } from "@/types"

const serviceCategories: Array<{ value: ServiceCategory; label: string }> = [
  { value: "event-photographers", label: "Event Photography" },
  { value: "wedding-photographers", label: "Wedding Photography" },
  { value: "wedding-videographers", label: "Wedding Videography" },
  { value: "event-videographers", label: "Event Videography" },
  { value: "wedding-dronography", label: "Wedding Dronography" },
  { value: "event-dronography", label: "Event Dronography" },
  { value: "vehicle-renting", label: "Vehicle Renting" },
  { value: "flower-decorators", label: "Flower Decorators" },
  { value: "makeup-artists", label: "Makeup Artists" },
  { value: "wedding-dress-tailoring", label: "Wedding Dress Services" },
  { value: "product-shoot-models", label: "Product Shoot Models" },
  { value: "product-photography", label: "Product Photography" },
  { value: "photo-framing", label: "Photo Framing" },
  { value: "photo-video-editing", label: "Photo & Video Editing" },
  { value: "album-making", label: "Album Making" },
]

export default function SellerProfilePage() {
  const { user } = useAuth()
  const [formData, setFormData] = useState({
    name: "",
    businessName: "",
    description: "",
    phone: "",
    whatsapp: "",
    categories: [] as ServiceCategory[],
  })
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || "",
        businessName: user.businessName || "",
        description: user.description || "",
        phone: user.phone || "",
        whatsapp: user.whatsapp || "",
        categories: user.categories || [],
      })
    }
  }, [user])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleCategoryChange = (category: ServiceCategory, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      categories: checked ? [...prev.categories, category] : prev.categories.filter((c) => c !== category),
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    setLoading(true)

    try {
      await updateDoc(doc(db, "users", user.id), {
        ...formData,
        updatedAt: new Date(),
      })

      toast({
        title: "Profile Updated",
        description: "Your profile has been updated successfully",
      })
    } catch (error) {
      console.error("Error updating profile:", error)
      toast({
        title: "Update Failed",
        description: "Failed to update profile",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <SellerLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Profile Settings</h1>
          <p className="text-gray-400">Manage your business profile and information</p>
        </div>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-white">Business Information</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white">Personal Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name" className="text-gray-300">
                      Full Name
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      type="text"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="bg-gray-800 border-gray-700 text-white"
                    />
                  </div>
                  <div>
                    <Label htmlFor="businessName" className="text-gray-300">
                      Business Name
                    </Label>
                    <Input
                      id="businessName"
                      name="businessName"
                      type="text"
                      value={formData.businessName}
                      onChange={handleInputChange}
                      className="bg-gray-800 border-gray-700 text-white"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="phone" className="text-gray-300">
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="bg-gray-800 border-gray-700 text-white"
                    />
                  </div>
                  <div>
                    <Label htmlFor="whatsapp" className="text-gray-300">
                      WhatsApp Number
                    </Label>
                    <Input
                      id="whatsapp"
                      name="whatsapp"
                      type="tel"
                      value={formData.whatsapp}
                      onChange={handleInputChange}
                      className="bg-gray-800 border-gray-700 text-white"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="description" className="text-gray-300">
                    Business Description
                  </Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={4}
                    className="bg-gray-800 border-gray-700 text-white"
                    placeholder="Describe your business and services..."
                  />
                </div>
              </div>

              {/* Service Categories */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white">Service Categories</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {serviceCategories.map((category) => (
                    <div key={category.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={category.value}
                        checked={formData.categories.includes(category.value)}
                        onCheckedChange={(checked) => handleCategoryChange(category.value, checked as boolean)}
                        className="border-gray-600"
                      />
                      <Label htmlFor={category.value} className="text-gray-300 text-sm">
                        {category.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <Button type="submit" disabled={loading}>
                {loading ? "Updating..." : "Update Profile"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </SellerLayout>
  )
}
