rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserData() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
    }
    
    function isAdmin() {
      return isAuthenticated() && getUserData().role == 'admin';
    }
    
    function isSeller() {
      return isAuthenticated() && getUserData().role == 'seller';
    }
    
    function isApprovedSeller() {
      return isSeller() && getUserData().isApproved == true;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isSellerOwner(sellerId) {
      return isAuthenticated() && request.auth.uid == sellerId;
    }

    // Users collection rules
    match /users/{userId} {
      // Allow users to read their own profile
      allow read: if isOwner(userId);
      
      // Allow users to create their own profile during signup
      allow create: if isAuthenticated() && isOwner(userId);
      
      // Allow users to update their own profile
      allow update: if isOwner(userId);
      
      // Allow admins to read all users (for seller management)
      allow read: if isAdmin();
      
      // Allow admins to update user approval status
      allow update: if isAdmin();
      
      // Allow admins to delete users
      allow delete: if isAdmin();
    }

    // Advertisements collection rules
    match /advertisements/{adId} {
      // Anyone can read active advertisements
      allow read: if resource.data.isActive == true;
      
      // Admins can read all advertisements
      allow read: if isAdmin();
      
      // Approved sellers can read their own advertisements
      allow read: if isApprovedSeller() && isSellerOwner(resource.data.sellerId);
      
      // Approved sellers can create advertisements
      allow create: if isApprovedSeller() && isSellerOwner(resource.data.sellerId);
      
      // Sellers can update their own advertisements
      allow update: if isApprovedSeller() && isSellerOwner(resource.data.sellerId);
      
      // Sellers can delete their own advertisements
      allow delete: if isApprovedSeller() && isSellerOwner(resource.data.sellerId);
      
      // Admins can update/delete any advertisement
      allow update, delete: if isAdmin();
    }

    // Bookings collection rules
    match /bookings/{bookingId} {
      // Customers can read bookings with their phone number
      allow read: if resource.data.customerPhone == request.auth.token.phone_number;
      
      // Allow reading bookings by customer phone (for my-bookings page)
      allow read: if true; // This is open for now since customers don't have accounts
      
      // Sellers can read their own bookings
      allow read: if isApprovedSeller() && isSellerOwner(resource.data.sellerId);
      
      // Sellers can update their own bookings (status changes)
      allow update: if isApprovedSeller() && isSellerOwner(resource.data.sellerId);
      
      // Admins can read all bookings
      allow read: if isAdmin();
      
      // Admins can update any booking
      allow update: if isAdmin();
      
      // Anyone can create bookings (customers don't need accounts)
      allow create: if true;
      
      // Admins can delete bookings
      allow delete: if isAdmin();
    }

    // Payments collection rules (if you add this later)
    match /payments/{paymentId} {
      // Sellers can read their own payments
      allow read: if isApprovedSeller() && isSellerOwner(resource.data.sellerId);
      
      // Admins can read all payments
      allow read: if isAdmin();
      
      // System can create payments
      allow create: if isAuthenticated();
      
      // Admins can update payments
      allow update: if isAdmin();
    }

    // Reviews collection rules (if you add this later)
    match /reviews/{reviewId} {
      // Anyone can read reviews
      allow read: if true;
      
      // Authenticated users can create reviews
      allow create: if isAuthenticated();
      
      // Users can update their own reviews
      allow update: if isOwner(resource.data.userId);
      
      // Admins can delete reviews
      allow delete: if isAdmin();
    }

    // Default deny rule for any other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
