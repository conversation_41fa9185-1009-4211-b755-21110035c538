"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { doc, getDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { useCart } from "@/contexts/CartContext"
import { Header } from "@/components/Header"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, MapPin, Clock, Check, ShoppingCart } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import type { Advertisement } from "@/types"

export default function ServiceDetailPage() {
  const params = useParams()
  const serviceId = params.id as string
  const [service, setService] = useState<Advertisement | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null)
  const { addItem } = useCart()

  useEffect(() => {
    const fetchService = async () => {
      try {
        const serviceDoc = await getDoc(doc(db, "advertisements", serviceId))
        if (serviceDoc.exists()) {
          const serviceData = {
            id: serviceDoc.id,
            ...serviceDoc.data(),
            createdAt: serviceDoc.data().createdAt?.toDate(),
            updatedAt: serviceDoc.data().updatedAt?.toDate(),
          } as Advertisement

          setService(serviceData)
        }
      } catch (error) {
        console.error("Error fetching service:", error)
      } finally {
        setLoading(false)
      }
    }

    if (serviceId) {
      fetchService()
    }
  }, [serviceId])

  const handleAddToCart = (packageId: string) => {
    if (!service) return

    const selectedPkg = service.packages.find((pkg) => pkg.id === packageId)
    if (!selectedPkg) return

    addItem({
      advertisementId: service.id,
      packageId: packageId,
      sellerId: service.sellerId,
      packageName: selectedPkg.name,
      price: selectedPkg.price,
      sellerName: service.title, // We'll need to fetch seller name in a real app
      category: service.category,
    })

    toast({
      title: "Added to Cart",
      description: `${selectedPkg.name} has been added to your cart`,
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-black">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="text-white">Loading service details...</div>
        </div>
      </div>
    )
  }

  if (!service) {
    return (
      <div className="min-h-screen bg-black">
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="text-white">Service not found</div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Service Images */}
          <div className="space-y-4">
            <div className="aspect-video relative overflow-hidden rounded-lg">
              <img
                src={
                  service.coverMedia || "/placeholder.svg?height=400&width=600&query=professional photography service"
                }
                alt={service.title}
                className="w-full h-full object-cover"
              />
            </div>
            {service.gallery.length > 0 && (
              <div className="grid grid-cols-3 gap-2">
                {service.gallery.slice(0, 6).map((image, index) => (
                  <div key={index} className="aspect-square relative overflow-hidden rounded-lg">
                    <img
                      src={image || "/placeholder.svg"}
                      alt={`Gallery ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Service Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">{service.title}</h1>
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center gap-1">
                  <Star className="w-5 h-5 text-yellow-500 fill-current" />
                  <span className="text-white">4.8</span>
                  <span className="text-gray-400">(24 reviews)</span>
                </div>
                <div className="flex items-center gap-1 text-gray-300">
                  <MapPin className="w-4 h-4" />
                  <span>{service.location}</span>
                </div>
              </div>
              <p className="text-gray-300 leading-relaxed">{service.description}</p>
            </div>

            {/* Service Packages */}
            <div>
              <h2 className="text-2xl font-semibold text-white mb-4">Service Packages</h2>
              <div className="space-y-4">
                {service.packages.map((pkg) => (
                  <Card
                    key={pkg.id}
                    className={`bg-gray-900 border-gray-800 cursor-pointer transition-all ${
                      selectedPackage === pkg.id ? "border-white" : "hover:border-gray-600"
                    }`}
                    onClick={() => setSelectedPackage(pkg.id)}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="text-xl font-semibold text-white mb-1">{pkg.name}</h3>
                          <p className="text-gray-400 text-sm">{pkg.description}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-white">LKR {pkg.price.toLocaleString()}</div>
                          {pkg.duration && (
                            <div className="flex items-center gap-1 text-gray-400 text-sm">
                              <Clock className="w-4 h-4" />
                              <span>{pkg.duration}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {pkg.features.length > 0 && (
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium text-gray-300">What's included:</h4>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-1">
                            {pkg.features.map((feature, index) => (
                              <div key={index} className="flex items-center gap-2 text-sm text-gray-400">
                                <Check className="w-4 h-4 text-green-500" />
                                <span>{feature}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="mt-4 pt-4 border-t border-gray-800">
                        <Button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleAddToCart(pkg.id)
                          }}
                          className="w-full flex items-center gap-2"
                        >
                          <ShoppingCart className="w-4 h-4" />
                          Add to Cart
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Category-Specific Information */}
            {service.categorySpecific && Object.keys(service.categorySpecific).length > 0 && (
              <div>
                <h2 className="text-2xl font-semibold text-white mb-4">Additional Information</h2>
                <Card className="bg-gray-900 border-gray-800">
                  <CardContent className="p-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {Object.entries(service.categorySpecific).map(([key, value]) => (
                        <div key={key}>
                          <dt className="text-sm font-medium text-gray-400 capitalize">
                            {key.replace(/([A-Z])/g, " $1").trim()}
                          </dt>
                          <dd className="text-white">
                            {Array.isArray(value) ? (
                              <div className="flex flex-wrap gap-1 mt-1">
                                {value.map((item, index) => (
                                  <Badge key={index} variant="outline" className="text-xs">
                                    {item}
                                  </Badge>
                                ))}
                              </div>
                            ) : (
                              value
                            )}
                          </dd>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
