"use client"

import { useEffect, useState } from "react"
import { collection, getDocs, query, where } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { AdminLayout } from "@/components/admin/AdminLayout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, Camera, ShoppingCart, CreditCard, TrendingUp, Calendar } from "lucide-react"

interface DashboardStats {
  totalSellers: number
  activeSellers: number
  totalServices: number
  activeServices: number
  totalBookings: number
  pendingBookings: number
  totalRevenue: number
  monthlyRevenue: number
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalSellers: 0,
    activeSellers: 0,
    totalServices: 0,
    activeServices: 0,
    totalBookings: 0,
    pendingBookings: 0,
    totalRevenue: 0,
    monthlyRevenue: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Fetch sellers
        const sellersQuery = query(collection(db, "users"), where("role", "==", "seller"))
        const sellersSnapshot = await getDocs(sellersQuery)
        const sellers = sellersSnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))

        // Fetch services
        const servicesSnapshot = await getDocs(collection(db, "advertisements"))
        const services = servicesSnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))

        // Fetch bookings
        const bookingsSnapshot = await getDocs(collection(db, "bookings"))
        const bookings = bookingsSnapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))

        // Calculate stats
        const activeSellers = sellers.filter((seller: any) => seller.isApproved).length
        const activeServices = services.filter((service: any) => service.isActive).length
        const pendingBookings = bookings.filter((booking: any) => booking.status === "waiting").length

        // Calculate revenue (this would be more complex in a real app)
        const totalRevenue = bookings
          .filter((booking: any) => booking.status === "paid")
          .reduce((sum: number, booking: any) => sum + (booking.totalAmount || 0), 0)

        // Calculate monthly revenue (last 30 days)
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        const monthlyRevenue = bookings
          .filter((booking: any) => {
            const bookingDate = booking.createdAt?.toDate?.() || new Date(booking.createdAt)
            return booking.status === "paid" && bookingDate >= thirtyDaysAgo
          })
          .reduce((sum: number, booking: any) => sum + (booking.totalAmount || 0), 0)

        setStats({
          totalSellers: sellers.length,
          activeSellers,
          totalServices: services.length,
          activeServices,
          totalBookings: bookings.length,
          pendingBookings,
          totalRevenue,
          monthlyRevenue,
        })
      } catch (error) {
        console.error("Error fetching dashboard stats:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const statCards = [
    {
      title: "Total Sellers",
      value: stats.totalSellers,
      subtitle: `${stats.activeSellers} active`,
      icon: Users,
      color: "text-blue-500",
    },
    {
      title: "Total Services",
      value: stats.totalServices,
      subtitle: `${stats.activeServices} active`,
      icon: Camera,
      color: "text-green-500",
    },
    {
      title: "Total Bookings",
      value: stats.totalBookings,
      subtitle: `${stats.pendingBookings} pending`,
      icon: ShoppingCart,
      color: "text-yellow-500",
    },
    {
      title: "Total Revenue",
      value: `LKR ${stats.totalRevenue.toLocaleString()}`,
      subtitle: `LKR ${stats.monthlyRevenue.toLocaleString()} this month`,
      icon: CreditCard,
      color: "text-purple-500",
    },
  ]

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
          <p className="text-gray-400">Welcome to the admin dashboard</p>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="bg-gray-900 border-gray-800 animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-800 rounded mb-2" />
                  <div className="h-8 bg-gray-800 rounded mb-2" />
                  <div className="h-3 bg-gray-800 rounded w-2/3" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {statCards.map((card, index) => (
              <Card key={index} className="bg-gray-900 border-gray-800">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-400">{card.title}</p>
                      <p className="text-2xl font-bold text-white">{card.value}</p>
                      <p className="text-xs text-gray-500">{card.subtitle}</p>
                    </div>
                    <card.icon className={`w-8 h-8 ${card.color}`} />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <div className="flex-1">
                    <p className="text-sm text-white">New seller registration</p>
                    <p className="text-xs text-gray-400">2 hours ago</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                  <div className="flex-1">
                    <p className="text-sm text-white">New booking received</p>
                    <p className="text-xs text-gray-400">4 hours ago</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-800 rounded-lg">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                  <div className="flex-1">
                    <p className="text-sm text-white">Payment completed</p>
                    <p className="text-xs text-gray-400">6 hours ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <button className="w-full p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-left transition-colors">
                  <p className="text-sm font-medium text-white">Generate Registration Link</p>
                  <p className="text-xs text-gray-400">Create a new seller registration link</p>
                </button>
                <button className="w-full p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-left transition-colors">
                  <p className="text-sm font-medium text-white">Review Pending Bookings</p>
                  <p className="text-xs text-gray-400">Check bookings waiting for approval</p>
                </button>
                <button className="w-full p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-left transition-colors">
                  <p className="text-sm font-medium text-white">Manage Sellers</p>
                  <p className="text-xs text-gray-400">Approve or manage seller accounts</p>
                </button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  )
}
