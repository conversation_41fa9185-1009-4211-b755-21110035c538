"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useAuth } from "@/contexts/AuthContext"
import { toast } from "@/hooks/use-toast"
import { LogIn, Shield, Store, User } from "lucide-react"

export default function LoginPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const { signIn, user, loading: authLoading } = useAuth()
  const router = useRouter()

  // Redirect if already logged in
  useEffect(() => {
    if (!authLoading && user) {
      switch (user.role) {
        case "admin":
          router.push("/admin")
          break
        case "seller":
          router.push("/seller")
          break
        case "user":
          router.push("/")
          break
        default:
          router.push("/")
      }
    }
  }, [user, authLoading, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      await signIn(email, password)
      // Redirect will be handled by the useEffect above
    } catch (error: any) {
      toast({
        title: "Login Failed",
        description: error.message || "Invalid email or password",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  if (authLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-gray-900 border-gray-800">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <LogIn className="w-8 h-8 text-white" />
            <div className="text-2xl font-bold text-white">MALKA</div>
            <div className="text-sm text-gray-400">STUDIO</div>
          </div>
          <CardTitle className="text-white text-2xl">Welcome Back</CardTitle>
          <p className="text-gray-400">Sign in to your account</p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="email" className="text-gray-300">
                  Email Address
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="bg-gray-800 border-gray-700 text-white"
                  placeholder="Enter your email"
                />
              </div>
              <div>
                <Label htmlFor="password" className="text-gray-300">
                  Password
                </Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="bg-gray-800 border-gray-700 text-white"
                  placeholder="Enter your password"
                />
              </div>
            </div>

            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Signing in..." : "Sign In"}
            </Button>
          </form>

          <div className="mt-6 space-y-4">
            <div className="text-center">
              <p className="text-gray-400 text-sm">
                Don't have an account?{" "}
                <Link href="/signup" className="text-blue-400 hover:text-blue-300">
                  Create one here
                </Link>
              </p>
            </div>

            <div className="border-t border-gray-700 pt-4">
              <p className="text-gray-400 text-sm text-center mb-3">
                Quick access for different user types:
              </p>
              <div className="grid grid-cols-3 gap-2">
                <Link href="/admin/login">
                  <Button variant="outline" size="sm" className="w-full bg-transparent border-gray-600 text-gray-300 hover:bg-gray-800">
                    <Shield className="w-4 h-4 mr-1" />
                    Admin
                  </Button>
                </Link>
                <Link href="/seller/login">
                  <Button variant="outline" size="sm" className="w-full bg-transparent border-gray-600 text-gray-300 hover:bg-gray-800">
                    <Store className="w-4 h-4 mr-1" />
                    Seller
                  </Button>
                </Link>
                <Button variant="outline" size="sm" className="w-full bg-transparent border-gray-600 text-gray-300 hover:bg-gray-800" disabled>
                  <User className="w-4 h-4 mr-1" />
                  User
                </Button>
              </div>
              <p className="text-gray-500 text-xs text-center mt-2">
                This unified login works for all account types
              </p>
            </div>

            <div className="text-center">
              <Link href="/" className="text-sm text-gray-400 hover:text-white">
                Back to Homepage
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
