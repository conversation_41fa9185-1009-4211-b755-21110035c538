"use client"

import { useEffect, useState } from "react"
import { collection, query, where, orderBy, limit, getDocs } from "firebase/firestore"
import { getDb } from "@/lib/firebase"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Star, MapPin } from "lucide-react"
import Link from "next/link"
import type { Advertisement } from "@/types"

export function FeaturedServices() {
  const [services, setServices] = useState<Advertisement[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchFeaturedServices = async () => {
      try {
        const db = getDb()
        const q = query(
          collection(db, "advertisements"),
          where("isActive", "==", true),
          orderBy("createdAt", "desc"),
          limit(6),
        )

        const querySnapshot = await getDocs(q)
        const servicesData = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate(),
          updatedAt: doc.data().updatedAt?.toDate(),
        })) as Advertisement[]

        setServices(servicesData)
      } catch (error) {
        console.error("Error fetching featured services:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchFeaturedServices()
  }, [])

  if (loading) {
    return (
      <section className="py-20 bg-gray-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-light text-white mb-6">Featured Services</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="bg-gray-900 border-gray-800 animate-pulse">
                <div className="aspect-video bg-gray-800" />
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-800 rounded mb-2" />
                  <div className="h-3 bg-gray-800 rounded mb-4 w-2/3" />
                  <div className="h-8 bg-gray-800 rounded" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-gray-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <p className="text-sm text-gray-400 uppercase tracking-wider mb-4">FEATURED</p>
          <h2 className="text-4xl md:text-5xl font-light text-white mb-6">Popular Services</h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            Discover the most sought-after photography services from our talented professionals
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service) => (
            <Link key={service.id} href={`/service/${service.id}`}>
              <Card className="bg-gray-900 border-gray-800 hover:bg-gray-800 transition-all duration-300 group cursor-pointer overflow-hidden">
                <div className="aspect-video relative overflow-hidden">
                  <img
                    src={
                      service.coverMedia ||
                      "/placeholder.svg?height=300&width=400&query=professional photography service" ||
                      "/placeholder.svg"
                    }
                    alt={service.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center gap-2 text-white text-sm">
                      <MapPin className="w-4 h-4" />
                      <span>{service.location}</span>
                    </div>
                  </div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-gray-200 transition-colors">
                    {service.title}
                  </h3>
                  <p className="text-gray-400 text-sm mb-4 line-clamp-2">{service.description}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      <span className="text-sm text-gray-300">4.8</span>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-400">Starting from</p>
                      <p className="text-lg font-semibold text-white">
                        LKR {Math.min(...service.packages.map((p) => p.price)).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link href="/services">
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-black bg-transparent"
            >
              VIEW ALL SERVICES
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
