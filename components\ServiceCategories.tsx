"use client"

import type React from "react"

import { <PERSON>, CardContent } from "@/components/ui/card"
import { Camera, Video, Plane, Car, Flower, Palette, Shirt, User, Frame, Edit, BookOpen } from "lucide-react"
import Link from "next/link"
import type { ServiceCategory } from "@/types"

const categoryData: Array<{
  category: ServiceCategory
  title: string
  description: string
  icon: React.ReactNode
  number: string
}> = [
  {
    category: "event-photographers",
    title: "Event Photography",
    description: "Professional event coverage and candid moments",
    icon: <Camera className="w-8 h-8" />,
    number: "01",
  },
  {
    category: "wedding-photographers",
    title: "Wedding Photography",
    description: "Capture your special day with artistic excellence",
    icon: <Camera className="w-8 h-8" />,
    number: "02",
  },
  {
    category: "wedding-videographers",
    title: "Wedding Videography",
    description: "Cinematic wedding films and highlight reels",
    icon: <Video className="w-8 h-8" />,
    number: "03",
  },
  {
    category: "event-videographers",
    title: "Event Videography",
    description: "Professional video coverage for all events",
    icon: <Video className="w-8 h-8" />,
    number: "04",
  },
  {
    category: "wedding-dronography",
    title: "Wedding Dronography",
    description: "Aerial wedding photography and videography",
    icon: <Plane className="w-8 h-8" />,
    number: "05",
  },
  {
    category: "event-dronography",
    title: "Event Dronography",
    description: "Stunning aerial perspectives for events",
    icon: <Plane className="w-8 h-8" />,
    number: "06",
  },
  {
    category: "vehicle-renting",
    title: "Vehicle Renting",
    description: "Luxury vehicles for weddings and photo shoots",
    icon: <Car className="w-8 h-8" />,
    number: "07",
  },
  {
    category: "flower-decorators",
    title: "Flower Decorators",
    description: "Beautiful floral arrangements and decorations",
    icon: <Flower className="w-8 h-8" />,
    number: "08",
  },
  {
    category: "makeup-artists",
    title: "Makeup Artists",
    description: "Professional makeup for all occasions",
    icon: <Palette className="w-8 h-8" />,
    number: "09",
  },
  {
    category: "wedding-dress-tailoring",
    title: "Wedding Dress Services",
    description: "Custom tailoring and dress rentals",
    icon: <Shirt className="w-8 h-8" />,
    number: "10",
  },
  {
    category: "product-shoot-models",
    title: "Product Shoot Models",
    description: "Professional models for product photography",
    icon: <User className="w-8 h-8" />,
    number: "11",
  },
  {
    category: "product-photography",
    title: "Product Photography",
    description: "High-quality product and commercial photography",
    icon: <Camera className="w-8 h-8" />,
    number: "12",
  },
  {
    category: "photo-framing",
    title: "Photo Framing",
    description: "Custom framing and printing services",
    icon: <Frame className="w-8 h-8" />,
    number: "13",
  },
  {
    category: "photo-video-editing",
    title: "Photo & Video Editing",
    description: "Professional post-production services",
    icon: <Edit className="w-8 h-8" />,
    number: "14",
  },
  {
    category: "album-making",
    title: "Album Making",
    description: "Custom photo albums and memory books",
    icon: <BookOpen className="w-8 h-8" />,
    number: "15",
  },
]

export function ServiceCategories() {
  return (
    <section className="py-20 bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <p className="text-sm text-gray-400 uppercase tracking-wider mb-4">OUR SPECIALITIES</p>
          <h2 className="text-4xl md:text-5xl font-light text-white mb-6">
            Special Services
            <br />
            <span className="font-normal">We Offer!</span>
          </h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            Discover our comprehensive range of photography and event services. From capturing precious moments to
            creating unforgettable experiences.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {categoryData.map((item) => (
            <Link key={item.category} href={`/category/${item.category}`}>
              <Card className="bg-gray-900/50 border-gray-800 hover:bg-gray-800/50 transition-all duration-300 group cursor-pointer h-full">
                <CardContent className="p-6 flex flex-col h-full">
                  <div className="flex items-start justify-between mb-4">
                    <div className="text-white group-hover:text-gray-300 transition-colors">{item.icon}</div>
                    <span className="text-sm text-gray-500 font-mono">{item.number}</span>
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-gray-200 transition-colors">
                    {item.title}
                  </h3>
                  <p className="text-gray-400 text-sm leading-relaxed flex-1">{item.description}</p>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
}
