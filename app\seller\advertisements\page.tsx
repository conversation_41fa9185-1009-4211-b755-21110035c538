"use client"

import { useEffect, useState } from "react"
import { collection, getDocs, query, where, doc, deleteDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { useAuth } from "@/contexts/AuthContext"
import { SellerLayout } from "@/components/seller/SellerLayout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Edit, Trash2, Eye } from "lucide-react"
import Link from "next/link"
import { toast } from "@/hooks/use-toast"
import type { Advertisement } from "@/types"

export default function SellerAdvertisementsPage() {
  const { user } = useAuth()
  const [advertisements, setAdvertisements] = useState<Advertisement[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchAdvertisements = async () => {
      if (!user) return

      try {
        if (!db) {
          console.error("Database not initialized")
          setLoading(false)
          return
        }

        const q = query(collection(db, "advertisements"), where("sellerId", "==", user.id))
        const querySnapshot = await getDocs(q)
        const adsData = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate(),
          updatedAt: doc.data().updatedAt?.toDate(),
        })) as Advertisement[]

        setAdvertisements(adsData)
      } catch (error) {
        console.error("Error fetching advertisements:", error)
        toast({
          title: "Error",
          description: "Failed to fetch advertisements",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchAdvertisements()
  }, [user])

  const handleDelete = async (adId: string) => {
    if (!confirm("Are you sure you want to delete this advertisement?")) return

    try {
      await deleteDoc(doc(db, "advertisements", adId))
      setAdvertisements((prev) => prev.filter((ad) => ad.id !== adId))
      toast({
        title: "Success",
        description: "Advertisement deleted successfully",
      })
    } catch (error) {
      console.error("Error deleting advertisement:", error)
      toast({
        title: "Error",
        description: "Failed to delete advertisement",
        variant: "destructive",
      })
    }
  }

  const getCategoryLabel = (category: string) => {
    const categoryMap: Record<string, string> = {
      "event-photographers": "Event Photography",
      "wedding-photographers": "Wedding Photography",
      "wedding-videographers": "Wedding Videography",
      "event-videographers": "Event Videography",
      "wedding-dronography": "Wedding Dronography",
      "event-dronography": "Event Dronography",
      "vehicle-renting": "Vehicle Renting",
      "flower-decorators": "Flower Decorators",
      "makeup-artists": "Makeup Artists",
      "wedding-dress-tailoring": "Wedding Dress Services",
      "product-shoot-models": "Product Shoot Models",
      "product-photography": "Product Photography",
      "photo-framing": "Photo Framing",
      "photo-video-editing": "Photo & Video Editing",
      "album-making": "Album Making",
    }
    return categoryMap[category] || category
  }

  return (
    <SellerLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Advertisements</h1>
            <p className="text-gray-400">Manage your service advertisements</p>
          </div>
          <Link href="/seller/advertisements/create">
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Create Advertisement
            </Button>
          </Link>
        </div>

        {/* Account Status Check */}
        {user && !user.isApproved && (
          <Card className="bg-yellow-900/20 border-yellow-800">
            <CardContent className="p-4">
              <p className="text-yellow-200">
                Your account is pending approval. You can create advertisements, but they won't be visible to users
                until your account is approved.
              </p>
            </CardContent>
          </Card>
        )}

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-white">Your Advertisements ({advertisements.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="text-gray-400">Loading advertisements...</div>
              </div>
            ) : advertisements.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">No advertisements yet</div>
                <Link href="/seller/advertisements/create">
                  <Button>Create Your First Advertisement</Button>
                </Link>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-gray-800">
                      <TableHead className="text-gray-300">Title</TableHead>
                      <TableHead className="text-gray-300">Category</TableHead>
                      <TableHead className="text-gray-300">Location</TableHead>
                      <TableHead className="text-gray-300">Packages</TableHead>
                      <TableHead className="text-gray-300">Status</TableHead>
                      <TableHead className="text-gray-300">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {advertisements.map((ad) => (
                      <TableRow key={ad.id} className="border-gray-800">
                        <TableCell className="text-white font-medium">{ad.title}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{getCategoryLabel(ad.category)}</Badge>
                        </TableCell>
                        <TableCell className="text-gray-300">{ad.location}</TableCell>
                        <TableCell className="text-gray-300">{ad.packages.length} packages</TableCell>
                        <TableCell>
                          <Badge
                            variant={ad.isActive ? "default" : "secondary"}
                            className={ad.isActive ? "bg-green-600" : "bg-gray-600"}
                          >
                            {ad.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Link href={`/service/${ad.id}`}>
                              <Button variant="ghost" size="sm">
                                <Eye className="w-4 h-4" />
                              </Button>
                            </Link>
                            <Link href={`/seller/advertisements/edit/${ad.id}`}>
                              <Button variant="ghost" size="sm">
                                <Edit className="w-4 h-4" />
                              </Button>
                            </Link>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(ad.id)}
                              className="text-red-500"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </SellerLayout>
  )
}
